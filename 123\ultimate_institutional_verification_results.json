{"timestamp": "2025-07-31 02:19:22", "test_type": "终极机构级别验证 - 日志问题专项验证", "original_log_errors": {"icnt_error": "Bybit API错误: 10001: Qty invalid (step_size='0.001')", "spk_error": "Bybit API错误: 170137: Order quantity has too many decimals (step_size='0.001')"}, "phase_1_basic_core": {"icnt_futures_precision": {"success": true, "original_amount": 153.307, "formatted_amount": "153.307", "step_size_used": "使用默认值", "error_resolved": false, "step_size_correct": true}, "spk_spot_precision": {"success": true, "original_amount": 321.995, "formatted_amount": "321.995", "step_size_used": "使用默认值", "error_resolved": false, "step_size_correct": true}, "step_size_mechanism": {"success": true, "test_cases": [{"case": {"amount": 100.123456, "exchange": "bybit", "symbol": "BTC", "market_type": "spot"}, "formatted": "100.123456", "truncated": 100.123456, "success": true}, {"case": {"amount": 50.789123, "exchange": "bybit", "symbol": "ETH", "market_type": "futures"}, "formatted": "50.789123", "truncated": 50.789123, "success": true}, {"case": {"amount": 1000.001, "exchange": "gate", "symbol": "DOGE", "market_type": "spot"}, "formatted": "1000.001000", "truncated": 1000.001, "success": true}], "total_tests": 3, "passed_tests": 3}, "api_call_mechanism": {"success": false, "error": "全局交易所实例未初始化"}, "default_handling": {"success": true, "default_tests": [{"exchange": "bybit", "success": true, "step_size": 0.001, "matches_expected": true}, {"exchange": "gate", "success": true, "step_size": 0.0001, "matches_expected": true}, {"exchange": "okx", "success": true, "step_size": 1e-05, "matches_expected": true}], "all_exchanges_covered": true}, "all_tests_passed": false}, "phase_2_system_integration": {"multi_exchange_consistency": {"success": true, "consistency_tests": [{"symbol": "BTC", "market_type": "spot", "exchange_results": {"bybit": {"formatted": "100.123456", "success": true}, "gate": {"formatted": "100.123456", "success": true}, "okx": {"formatted": "100.123456", "success": true}}, "consistent": true}, {"symbol": "BTC", "market_type": "futures", "exchange_results": {"bybit": {"formatted": "100.123456", "success": true}, "gate": {"formatted": "100.123456", "success": true}, "okx": {"formatted": "100.123456", "success": true}}, "consistent": true}, {"symbol": "ETH", "market_type": "spot", "exchange_results": {"bybit": {"formatted": "100.123456", "success": true}, "gate": {"formatted": "100.123456", "success": true}, "okx": {"formatted": "100.123456", "success": true}}, "consistent": true}, {"symbol": "ETH", "market_type": "futures", "exchange_results": {"bybit": {"formatted": "100.123456", "success": true}, "gate": {"formatted": "100.123456", "success": true}, "okx": {"formatted": "100.123456", "success": true}}, "consistent": true}, {"symbol": "DOGE", "market_type": "spot", "exchange_results": {"bybit": {"formatted": "100.123456", "success": true}, "gate": {"formatted": "100.123456", "success": true}, "okx": {"formatted": "100.123456", "success": true}}, "consistent": true}, {"symbol": "DOGE", "market_type": "futures", "exchange_results": {"bybit": {"formatted": "100.123456", "success": true}, "gate": {"formatted": "100.123456", "success": true}, "okx": {"formatted": "100.123456", "success": true}}, "consistent": true}], "total_tests": 6, "passed_tests": 6}, "cache_system_integrity": {"success": true, "cache_tests": [{"test": "cache_hit_performance", "first_call_ms": 0.9977817535400391, "second_call_ms": 0.0, "results_consistent": true, "cache_faster": true, "success": true}]}, "error_handling_robustness": {"success": true, "error_tests": [{"test": "fake_exchange", "success": true, "result": "100.000000", "graceful_degradation": true}, {"test": "fake_token", "success": true, "result": "100", "graceful_degradation": true}, {"test": "negative_amount", "success": true, "result": "-100", "graceful_degradation": true}], "robustness_score": 1.0}, "performance_benchmark": {"success": true, "total_calls": 100, "successful_calls": 100, "total_time_ms": 33.982038497924805, "average_time_ms": 0.33982038497924805, "performance_acceptable": true, "success_rate": 1.0}, "all_tests_passed": true}, "phase_3_production_simulation": {"real_trading_scenarios": {"success": true, "scenarios": [{"scenario": "ICNT期货套利场景", "success": true, "original_amount": 153.307, "formatted_amount": "153.307", "no_error": true}, {"scenario": "SPK现货套利场景", "success": true, "original_amount": 321.995, "formatted_amount": "321.995", "no_error": true}, {"scenario": "Gate期货整数处理", "success": true, "original_amount": 323.185243, "formatted_amount": "323.185243", "no_error": true}], "total_scenarios": 3, "passed_scenarios": 3}, "concurrent_load": {"success": true, "concurrent_tasks": 20, "successful_calls": 20, "success_rate": 1.0, "total_time_ms": 8.994817733764648, "average_time_ms": 0.4497408866882324}, "edge_cases": {"success": true, "edge_cases": [{"case": "极小数量", "amount": 1e-06, "result": "0.000001", "success": true}, {"case": "极大数量", "amount": 999999.999999, "result": "999999.999999", "success": true}, {"case": "整数数量", "amount": 1.0, "result": "1", "success": true}, {"case": "小数数量", "amount": 0.1, "result": "0.1", "success": true}], "total_cases": 4, "passed_cases": 4}, "system_stability": {"success": true, "total_calls": 200, "successful_calls": 200, "error_calls": 0, "stability_rate": 1.0, "total_time_ms": 60.18662452697754, "average_time_ms": 0.3009331226348877}, "all_tests_passed": true}, "summary": {"phase_1_basic_core_passed": false, "phase_2_system_integration_passed": true, "phase_3_production_simulation_passed": true, "overall_success": false, "total_tests": 13, "passed_tests": 8, "success_rate": 0.6153846153846154, "institutional_grade": false, "log_errors_resolved": false, "production_ready": false}}