#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
异步调用架构修复验证脚本
验证ICNT和SPK的步长问题是否得到解决
"""

import asyncio
import json
import time
import os
import sys
from typing import Dict, Any, Optional

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class AsyncArchitectureFixVerification:
    def __init__(self):
        self.results = {
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "verification_type": "异步调用架构修复验证",
            "test_results": {},
            "summary": {},
            "recommendations": []
        }
        
    async def verify_trading_rules_preloader_fix(self):
        """验证交易规则预加载器的异步调用修复"""
        print("🔍 验证交易规则预加载器异步调用修复...")
        
        try:
            from core.trading_rules_preloader import get_trading_rules_preloader
            preloader = get_trading_rules_preloader()
            
            # 测试关键代币的步长获取
            test_cases = [
                {"symbol": "ICNT", "exchange": "bybit", "market_type": "futures", "expected_step": 1.0},
                {"symbol": "SPK", "exchange": "bybit", "market_type": "spot", "expected_step": 0.1}
            ]
            
            for case in test_cases:
                symbol = case["symbol"]
                exchange = case["exchange"]
                market_type = case["market_type"]
                expected_step = case["expected_step"]
                
                print(f"📊 测试 {symbol}-USDT {exchange} {market_type}...")
                
                # 获取交易规则
                rule = preloader.get_trading_rule(exchange, symbol, market_type)
                
                if rule:
                    actual_step = float(rule.qty_step)
                    step_correct = abs(actual_step - expected_step) < 0.0001
                    
                    self.results["test_results"][f"{symbol}_{market_type}"] = {
                        "success": True,
                        "expected_step": expected_step,
                        "actual_step": actual_step,
                        "step_correct": step_correct,
                        "source": rule.source if hasattr(rule, 'source') else "unknown"
                    }
                    
                    status = "✅" if step_correct else "❌"
                    print(f"{status} {symbol}: 期望步长={expected_step}, 实际步长={actual_step}")
                else:
                    self.results["test_results"][f"{symbol}_{market_type}"] = {
                        "success": False,
                        "error": "无法获取交易规则"
                    }
                    print(f"❌ {symbol}: 无法获取交易规则")
                    
        except Exception as e:
            print(f"❌ 验证交易规则预加载器失败: {e}")
            self.results["test_results"]["preloader_test"] = {
                "success": False,
                "error": str(e)
            }
    
    async def verify_api_call_mechanism(self):
        """验证API调用机制"""
        print("🔍 验证API调用机制...")
        
        try:
            from core.trading_rules_preloader import get_trading_rules_preloader
            preloader = get_trading_rules_preloader()
            
            # 测试临时实例创建
            temp_instance = preloader._create_temporary_exchange_instance_sync("bybit")
            
            if temp_instance:
                print("✅ 临时交易所实例创建成功")
                
                # 测试同步API调用
                precision_info = preloader._get_precision_from_exchange_api_sync(
                    temp_instance, "ICNT", "futures"
                )
                
                if precision_info:
                    step_size = precision_info.get("step_size", 0)
                    source = precision_info.get("source", "unknown")
                    
                    self.results["test_results"]["api_call_test"] = {
                        "success": True,
                        "step_size": step_size,
                        "source": source,
                        "api_working": source == "api_sync"
                    }
                    
                    print(f"✅ API调用成功: step_size={step_size}, source={source}")
                else:
                    print("❌ API调用返回空结果")
                    self.results["test_results"]["api_call_test"] = {
                        "success": False,
                        "error": "API调用返回空结果"
                    }
            else:
                print("❌ 临时交易所实例创建失败")
                self.results["test_results"]["api_call_test"] = {
                    "success": False,
                    "error": "临时交易所实例创建失败"
                }
                
        except Exception as e:
            print(f"❌ 验证API调用机制失败: {e}")
            self.results["test_results"]["api_call_test"] = {
                "success": False,
                "error": str(e)
            }
    
    async def verify_execution_engine_compatibility(self):
        """验证执行引擎兼容性"""
        print("🔍 验证执行引擎兼容性...")
        
        try:
            from core.trading_rules_preloader import get_trading_rules_preloader
            preloader = get_trading_rules_preloader()
            
            # 测试数量格式化
            test_amounts = [153.307, 321.995]  # 之前失败的数量
            test_cases = [
                {"symbol": "ICNT", "exchange": "bybit", "market_type": "futures"},
                {"symbol": "SPK", "exchange": "bybit", "market_type": "spot"}
            ]
            
            for i, case in enumerate(test_cases):
                amount = test_amounts[i]
                symbol = case["symbol"]
                exchange = case["exchange"]
                market_type = case["market_type"]
                
                formatted = preloader.format_amount_unified(
                    amount, exchange, symbol, market_type
                )
                
                self.results["test_results"][f"format_{symbol}"] = {
                    "original_amount": amount,
                    "formatted_amount": formatted,
                    "success": formatted is not None
                }
                
                print(f"📊 {symbol}: {amount} → {formatted}")
                
        except Exception as e:
            print(f"❌ 验证执行引擎兼容性失败: {e}")
            self.results["test_results"]["execution_compatibility"] = {
                "success": False,
                "error": str(e)
            }
    
    def generate_summary(self):
        """生成验证摘要"""
        total_tests = len(self.results["test_results"])
        successful_tests = sum(1 for result in self.results["test_results"].values() 
                             if result.get("success", False))
        
        self.results["summary"] = {
            "total_tests": total_tests,
            "successful_tests": successful_tests,
            "success_rate": successful_tests / total_tests if total_tests > 0 else 0,
            "overall_status": "PASS" if successful_tests == total_tests else "FAIL"
        }
        
        # 生成建议
        if self.results["summary"]["success_rate"] < 1.0:
            self.results["recommendations"].append("部分测试失败，需要进一步修复异步调用架构")
        else:
            self.results["recommendations"].append("所有测试通过，异步调用架构修复成功")
    
    async def run_verification(self):
        """运行完整验证"""
        print("🚀 开始异步调用架构修复验证...")
        
        await self.verify_trading_rules_preloader_fix()
        await self.verify_api_call_mechanism()
        await self.verify_execution_engine_compatibility()
        
        self.generate_summary()
        
        # 保存结果
        output_file = "123/async_architecture_fix_verification_results.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, ensure_ascii=False, indent=2)
        
        print(f"📊 验证完成，结果已保存到: {output_file}")
        print(f"📈 成功率: {self.results['summary']['success_rate']:.1%}")
        print(f"🎯 总体状态: {self.results['summary']['overall_status']}")
        
        return self.results

async def main():
    """主函数"""
    verifier = AsyncArchitectureFixVerification()
    results = await verifier.run_verification()
    
    # 打印关键结果
    print("\n" + "="*50)
    print("🎯 关键验证结果:")
    for test_name, result in results["test_results"].items():
        if result.get("success"):
            print(f"✅ {test_name}: 成功")
        else:
            print(f"❌ {test_name}: 失败 - {result.get('error', '未知错误')}")
    
    return results

if __name__ == "__main__":
    asyncio.run(main())
