{"timestamp": "2025-07-31 02:13:58", "test_type": "最终接口兼容性验证", "interface_consistency": {"trading_rules_preloader": {"format_amount_unified": {"exists": true, "callable": true, "signature_check": true}, "truncate_to_step_size": {"exists": true, "callable": true, "signature_check": true}, "get_hedge_quality_cached": {"exists": true, "callable": true, "signature_check": true}, "format_amount_with_contract_conversion": {"exists": true, "callable": true, "signature_check": true}}, "universal_token_system": {"get_exchange_symbol_format": {"exists": true, "callable": true, "signature_check": true}, "is_symbol_supported": {"exists": true, "callable": true, "signature_check": true}, "normalize_symbol": {"exists": true, "callable": true, "signature_check": true}}, "all_interfaces_consistent": true}, "compatibility_check": {"method_compatibility": {"format_amount_unified": {"success": true, "result_type": "str", "expected_type": "str", "type_match": true}, "truncate_to_step_size": {"success": true, "result_type": "float", "expected_type": "float", "type_match": true}}, "symbol_support": {"BTC": {"supported": true, "type": "bool"}, "ETH": {"supported": true, "type": "bool"}, "DOGE": {"supported": true, "type": "bool"}, "UNKNOWN_TOKEN": {"supported": true, "type": "bool"}}, "backward_compatible": true, "forward_compatible": true}, "chain_integrity": {"chain_steps": {"step1_token_system": {"success": true, "is_supported": true, "exchange_format": "UNKNOWNUSDT"}, "step2_preloader": {"success": true, "formatted_amount": "0.123456", "truncated_amount": 0.123456}, "step3_error_handling": {"success": true, "graceful_degradation": true, "result": "0.123456"}}, "chain_complete": true, "error_handling_works": true}, "performance_validation": {"total_calls": 150, "average_time_ms": 0.3206459681193034, "max_time_ms": 1.0018348693847656, "min_time_ms": 0.0, "performance_acceptable": true, "all_calls_successful": true}, "summary": {"interface_consistency_passed": true, "compatibility_check_passed": true, "chain_integrity_passed": true, "performance_validation_passed": true, "overall_success": true, "final_verdict": "PERFECT_QUALITY"}}