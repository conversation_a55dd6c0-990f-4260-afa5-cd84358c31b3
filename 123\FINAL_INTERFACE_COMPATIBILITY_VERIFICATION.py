#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🏆 最终接口兼容性验证脚本
验证所有接口的统一性、兼容性，确保没有链路错误
"""

import sys
import os
import asyncio
import time
import json
from pathlib import Path
from typing import Dict, Any, List

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

class FinalInterfaceCompatibilityVerifier:
    """最终接口兼容性验证器"""
    
    def __init__(self):
        self.results = {
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "test_type": "最终接口兼容性验证",
            "interface_consistency": {},
            "compatibility_check": {},
            "chain_integrity": {},
            "performance_validation": {},
            "summary": {}
        }
        
    async def run_all_verifications(self):
        """运行所有验证"""
        print("🏆 开始最终接口兼容性验证...")
        
        # 1. 接口一致性验证
        await self._verify_interface_consistency()
        
        # 2. 兼容性检查
        await self._verify_compatibility()
        
        # 3. 链路完整性验证
        await self._verify_chain_integrity()
        
        # 4. 性能验证
        await self._verify_performance()
        
        # 5. 生成总结
        self._generate_summary()
        
        # 6. 保存结果
        self._save_results()
        
        print("✅ 最终接口兼容性验证完成！")
        return self.results
    
    async def _verify_interface_consistency(self):
        """验证接口一致性"""
        print("🔍 验证接口一致性...")
        
        try:
            # 验证TradingRulesPreloader接口
            from core.trading_rules_preloader import get_trading_rules_preloader
            preloader = get_trading_rules_preloader()
            
            # 检查核心方法存在性和签名
            core_methods = {
                "format_amount_unified": {
                    "exists": hasattr(preloader, 'format_amount_unified'),
                    "callable": callable(getattr(preloader, 'format_amount_unified', None)),
                    "signature_check": True
                },
                "truncate_to_step_size": {
                    "exists": hasattr(preloader, 'truncate_to_step_size'),
                    "callable": callable(getattr(preloader, 'truncate_to_step_size', None)),
                    "signature_check": True
                },
                "get_hedge_quality_cached": {
                    "exists": hasattr(preloader, 'get_hedge_quality_cached'),
                    "callable": callable(getattr(preloader, 'get_hedge_quality_cached', None)),
                    "signature_check": True
                },
                "format_amount_with_contract_conversion": {
                    "exists": hasattr(preloader, 'format_amount_with_contract_conversion'),
                    "callable": callable(getattr(preloader, 'format_amount_with_contract_conversion', None)),
                    "signature_check": True
                }
            }
            
            # 验证UniversalTokenSystem接口
            from core.universal_token_system import get_universal_token_system
            token_system = get_universal_token_system()
            
            token_methods = {
                "get_exchange_symbol_format": {
                    "exists": hasattr(token_system, 'get_exchange_symbol_format'),
                    "callable": callable(getattr(token_system, 'get_exchange_symbol_format', None)),
                    "signature_check": True
                },
                "is_symbol_supported": {
                    "exists": hasattr(token_system, 'is_symbol_supported'),
                    "callable": callable(getattr(token_system, 'is_symbol_supported', None)),
                    "signature_check": True
                },
                "normalize_symbol": {
                    "exists": hasattr(token_system, 'normalize_symbol'),
                    "callable": callable(getattr(token_system, 'normalize_symbol', None)),
                    "signature_check": True
                }
            }
            
            self.results["interface_consistency"] = {
                "trading_rules_preloader": core_methods,
                "universal_token_system": token_methods,
                "all_interfaces_consistent": all(
                    method["exists"] and method["callable"] 
                    for methods in [core_methods, token_methods] 
                    for method in methods.values()
                )
            }
            
            print(f"✅ 接口一致性验证完成")
            
        except Exception as e:
            print(f"❌ 接口一致性验证失败: {e}")
            self.results["interface_consistency"]["error"] = str(e)
    
    async def _verify_compatibility(self):
        """验证兼容性"""
        print("🔍 验证兼容性...")
        
        try:
            # 测试向后兼容性 - 原有接口仍然工作
            from core.trading_rules_preloader import get_trading_rules_preloader
            preloader = get_trading_rules_preloader()
            
            # 测试原有调用方式
            test_cases = [
                {
                    "method": "format_amount_unified",
                    "args": [100.123, "bybit", "BTC", "spot"],
                    "expected_type": str
                },
                {
                    "method": "truncate_to_step_size", 
                    "args": [100.123, "bybit", "BTC", "spot"],
                    "expected_type": float
                }
            ]
            
            compatibility_results = {}
            
            for case in test_cases:
                try:
                    method = getattr(preloader, case["method"])
                    result = method(*case["args"])
                    
                    compatibility_results[case["method"]] = {
                        "success": True,
                        "result_type": type(result).__name__,
                        "expected_type": case["expected_type"].__name__,
                        "type_match": isinstance(result, case["expected_type"])
                    }
                    
                except Exception as e:
                    compatibility_results[case["method"]] = {
                        "success": False,
                        "error": str(e)
                    }
            
            # 测试新功能不影响现有功能
            from core.universal_token_system import get_universal_token_system
            token_system = get_universal_token_system()
            
            # 测试代币支持检查
            symbol_tests = ["BTC", "ETH", "DOGE", "UNKNOWN_TOKEN"]
            symbol_results = {}
            
            for symbol in symbol_tests:
                try:
                    is_supported = token_system.is_symbol_supported(symbol)
                    symbol_results[symbol] = {
                        "supported": is_supported,
                        "type": type(is_supported).__name__
                    }
                except Exception as e:
                    symbol_results[symbol] = {"error": str(e)}
            
            self.results["compatibility_check"] = {
                "method_compatibility": compatibility_results,
                "symbol_support": symbol_results,
                "backward_compatible": all(
                    result.get("success", False) and result.get("type_match", False)
                    for result in compatibility_results.values()
                ),
                "forward_compatible": len([r for r in symbol_results.values() if "error" not in r]) > 0
            }
            
            print(f"✅ 兼容性验证完成")
            
        except Exception as e:
            print(f"❌ 兼容性验证失败: {e}")
            self.results["compatibility_check"]["error"] = str(e)
    
    async def _verify_chain_integrity(self):
        """验证链路完整性"""
        print("🔍 验证链路完整性...")
        
        try:
            # 测试完整的数据流链路
            from core.universal_token_system import get_universal_token_system
            from core.trading_rules_preloader import get_trading_rules_preloader
            
            token_system = get_universal_token_system()
            preloader = get_trading_rules_preloader()
            
            # 测试数据流: 配置 → 代币系统 → 预加载器 → 格式化
            test_symbol = "BTC"
            test_exchange = "bybit"
            test_amount = 0.123456
            
            chain_steps = {}
            
            # Step 1: 代币系统检查
            try:
                is_supported = token_system.is_symbol_supported(test_symbol)
                exchange_format = token_system.get_exchange_symbol_format(test_symbol, test_exchange, "spot")
                
                chain_steps["step1_token_system"] = {
                    "success": True,
                    "is_supported": is_supported,
                    "exchange_format": exchange_format
                }
            except Exception as e:
                chain_steps["step1_token_system"] = {"success": False, "error": str(e)}
            
            # Step 2: 预加载器处理
            try:
                formatted_amount = preloader.format_amount_unified(test_amount, test_exchange, test_symbol, "spot")
                truncated_amount = preloader.truncate_to_step_size(test_amount, test_exchange, test_symbol, "spot")
                
                chain_steps["step2_preloader"] = {
                    "success": True,
                    "formatted_amount": formatted_amount,
                    "truncated_amount": truncated_amount
                }
            except Exception as e:
                chain_steps["step2_preloader"] = {"success": False, "error": str(e)}
            
            # Step 3: 错误处理链路测试
            try:
                # 测试不存在的交易对
                fake_result = preloader.format_amount_unified(test_amount, "fake_exchange", "FAKE_TOKEN", "spot")
                
                chain_steps["step3_error_handling"] = {
                    "success": True,
                    "graceful_degradation": fake_result is not None,
                    "result": fake_result
                }
            except Exception as e:
                chain_steps["step3_error_handling"] = {"success": False, "error": str(e)}
            
            self.results["chain_integrity"] = {
                "chain_steps": chain_steps,
                "chain_complete": all(step.get("success", False) for step in chain_steps.values()),
                "error_handling_works": chain_steps.get("step3_error_handling", {}).get("graceful_degradation", False)
            }
            
            print(f"✅ 链路完整性验证完成")
            
        except Exception as e:
            print(f"❌ 链路完整性验证失败: {e}")
            self.results["chain_integrity"]["error"] = str(e)
    
    async def _verify_performance(self):
        """验证性能"""
        print("🔍 验证性能...")
        
        try:
            from core.trading_rules_preloader import get_trading_rules_preloader
            preloader = get_trading_rules_preloader()
            
            # 性能测试
            test_cases = [
                {"amount": 100.123, "exchange": "bybit", "symbol": "BTC", "market_type": "spot"},
                {"amount": 50.456, "exchange": "gate", "symbol": "ETH", "market_type": "futures"},
                {"amount": 200.789, "exchange": "okx", "symbol": "DOGE", "market_type": "spot"}
            ]
            
            performance_results = []
            total_time = 0
            successful_calls = 0
            
            for i in range(50):  # 50次调用测试
                for case in test_cases:
                    start_time = time.time()
                    try:
                        result = preloader.format_amount_unified(
                            case["amount"], case["exchange"], case["symbol"], case["market_type"]
                        )
                        end_time = time.time()
                        call_time = (end_time - start_time) * 1000  # 转换为毫秒
                        
                        performance_results.append(call_time)
                        total_time += call_time
                        successful_calls += 1
                        
                    except Exception as e:
                        print(f"性能测试调用失败: {e}")
            
            if successful_calls > 0:
                avg_time = total_time / successful_calls
                max_time = max(performance_results) if performance_results else 0
                min_time = min(performance_results) if performance_results else 0
                
                self.results["performance_validation"] = {
                    "total_calls": successful_calls,
                    "average_time_ms": avg_time,
                    "max_time_ms": max_time,
                    "min_time_ms": min_time,
                    "performance_acceptable": avg_time < 10.0,  # 小于10ms认为性能良好
                    "all_calls_successful": successful_calls == len(test_cases) * 50
                }
            else:
                self.results["performance_validation"] = {
                    "error": "所有性能测试调用都失败了"
                }
            
            print(f"✅ 性能验证完成")
            
        except Exception as e:
            print(f"❌ 性能验证失败: {e}")
            self.results["performance_validation"]["error"] = str(e)
    
    def _generate_summary(self):
        """生成总结"""
        interface_ok = self.results.get("interface_consistency", {}).get("all_interfaces_consistent", False)
        compatibility_ok = self.results.get("compatibility_check", {}).get("backward_compatible", False)
        chain_ok = self.results.get("chain_integrity", {}).get("chain_complete", False)
        performance_ok = self.results.get("performance_validation", {}).get("performance_acceptable", False)
        
        self.results["summary"] = {
            "interface_consistency_passed": interface_ok,
            "compatibility_check_passed": compatibility_ok,
            "chain_integrity_passed": chain_ok,
            "performance_validation_passed": performance_ok,
            "overall_success": all([interface_ok, compatibility_ok, chain_ok, performance_ok]),
            "final_verdict": "PERFECT_QUALITY" if all([interface_ok, compatibility_ok, chain_ok, performance_ok]) else "NEEDS_ATTENTION"
        }
    
    def _save_results(self):
        """保存结果"""
        try:
            results_file = project_root / "final_interface_compatibility_results.json"
            with open(results_file, 'w', encoding='utf-8') as f:
                json.dump(self.results, f, indent=2, ensure_ascii=False)
            print(f"📊 结果已保存到: {results_file}")
        except Exception as e:
            print(f"⚠️ 保存结果失败: {e}")

async def main():
    """主函数"""
    verifier = FinalInterfaceCompatibilityVerifier()
    results = await verifier.run_all_verifications()
    
    # 打印总结
    summary = results.get("summary", {})
    print("\n" + "="*60)
    print("🏆 最终接口兼容性验证结果")
    print("="*60)
    print(f"接口一致性: {'✅ 通过' if summary.get('interface_consistency_passed') else '❌ 失败'}")
    print(f"兼容性检查: {'✅ 通过' if summary.get('compatibility_check_passed') else '❌ 失败'}")
    print(f"链路完整性: {'✅ 通过' if summary.get('chain_integrity_passed') else '❌ 失败'}")
    print(f"性能验证: {'✅ 通过' if summary.get('performance_validation_passed') else '❌ 失败'}")
    print("-"*60)
    print(f"总体结果: {'🏆 完美质量' if summary.get('overall_success') else '⚠️ 需要关注'}")
    print("="*60)
    
    return results

if __name__ == "__main__":
    asyncio.run(main())
