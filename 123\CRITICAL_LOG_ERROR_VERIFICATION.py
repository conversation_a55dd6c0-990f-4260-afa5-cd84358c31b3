#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🚨 关键日志错误验证脚本
专门验证日志中的ICNT和SPK错误是否100%解决
重点验证：step_size不再是错误的0.001
"""

import sys
import os
import json
import time
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

class CriticalLogErrorVerifier:
    """关键日志错误验证器"""
    
    def __init__(self):
        self.results = {
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "verification_type": "关键日志错误专项验证",
            "original_errors": {
                "icnt_error": "Bybit API错误: 10001: Qty invalid (step_size='0.001')",
                "spk_error": "Bybit API错误: 170137: Order quantity has too many decimals (step_size='0.001')"
            },
            "verification_results": {},
            "final_verdict": {}
        }
    
    def run_verification(self):
        """运行验证"""
        print("🚨 开始关键日志错误专项验证...")
        
        # 验证1: ICNT期货精度问题
        icnt_result = self._verify_icnt_futures_precision()
        
        # 验证2: SPK现货精度问题
        spk_result = self._verify_spk_spot_precision()
        
        # 验证3: 步长获取机制
        step_size_result = self._verify_step_size_mechanism()
        
        # 验证4: 默认值问题
        default_result = self._verify_default_values()
        
        self.results["verification_results"] = {
            "icnt_futures_precision": icnt_result,
            "spk_spot_precision": spk_result,
            "step_size_mechanism": step_size_result,
            "default_values": default_result
        }
        
        # 生成最终判决
        self._generate_final_verdict()
        
        # 保存结果
        self._save_results()
        
        return self.results
    
    def _verify_icnt_futures_precision(self):
        """验证ICNT期货精度问题"""
        print("🔍 验证ICNT期货精度问题...")
        
        try:
            from core.trading_rules_preloader import get_trading_rules_preloader
            preloader = get_trading_rules_preloader()
            
            # 测试原始错误场景
            test_amount = 153.307  # 日志中的实际数量
            
            # 获取交易规则
            rule = preloader.get_trading_rule("bybit", "ICNT", "futures")
            
            # 格式化数量
            formatted = preloader.format_amount_unified(test_amount, "bybit", "ICNT", "futures")
            
            # 截取到步长
            truncated = preloader.truncate_to_step_size(test_amount, "bybit", "ICNT", "futures")
            
            result = {
                "success": True,
                "original_amount": test_amount,
                "rule_found": rule is not None,
                "step_size": rule.qty_step if rule else "未找到规则",
                "formatted_amount": formatted,
                "truncated_amount": truncated,
                "step_size_is_not_0_001": rule.qty_step != 0.001 if rule else False,
                "error_resolved": rule is not None and rule.qty_step != 0.001
            }
            
            print(f"📊 ICNT期货: 规则={'找到' if rule else '未找到'}, 步长={result['step_size']}, 格式化={formatted}")
            
            return result
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "error_resolved": False
            }
    
    def _verify_spk_spot_precision(self):
        """验证SPK现货精度问题"""
        print("🔍 验证SPK现货精度问题...")
        
        try:
            from core.trading_rules_preloader import get_trading_rules_preloader
            preloader = get_trading_rules_preloader()
            
            # 测试原始错误场景
            test_amount = 321.995  # 日志中的实际数量
            
            # 获取交易规则
            rule = preloader.get_trading_rule("bybit", "SPK", "spot")
            
            # 格式化数量
            formatted = preloader.format_amount_unified(test_amount, "bybit", "SPK", "spot")
            
            # 截取到步长
            truncated = preloader.truncate_to_step_size(test_amount, "bybit", "SPK", "spot")
            
            result = {
                "success": True,
                "original_amount": test_amount,
                "rule_found": rule is not None,
                "step_size": rule.qty_step if rule else "未找到规则",
                "formatted_amount": formatted,
                "truncated_amount": truncated,
                "step_size_is_not_0_001": rule.qty_step != 0.001 if rule else False,
                "error_resolved": rule is not None and rule.qty_step != 0.001
            }
            
            print(f"📊 SPK现货: 规则={'找到' if rule else '未找到'}, 步长={result['step_size']}, 格式化={formatted}")
            
            return result
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "error_resolved": False
            }
    
    def _verify_step_size_mechanism(self):
        """验证步长获取机制"""
        print("🔍 验证步长获取机制...")
        
        try:
            from core.trading_rules_preloader import get_trading_rules_preloader
            preloader = get_trading_rules_preloader()
            
            # 测试不同代币的步长获取
            test_cases = [
                {"exchange": "bybit", "symbol": "ICNT", "market_type": "futures"},
                {"exchange": "bybit", "symbol": "SPK", "market_type": "spot"},
                {"exchange": "bybit", "symbol": "BTC", "market_type": "spot"},
                {"exchange": "gate", "symbol": "BTC", "market_type": "spot"},
                {"exchange": "okx", "symbol": "BTC", "market_type": "spot"}
            ]
            
            results = []
            for case in test_cases:
                try:
                    rule = preloader.get_trading_rule(case["exchange"], case["symbol"], case["market_type"])
                    
                    results.append({
                        "case": case,
                        "rule_found": rule is not None,
                        "step_size": rule.qty_step if rule else "未找到",
                        "step_size_not_0_001": rule.qty_step != 0.001 if rule else False,
                        "success": True
                    })
                except Exception as e:
                    results.append({
                        "case": case,
                        "error": str(e),
                        "success": False
                    })
            
            # 检查是否有规则被找到且不是0.001
            rules_found = sum(1 for r in results if r.get("rule_found", False))
            correct_step_sizes = sum(1 for r in results if r.get("step_size_not_0_001", False))
            
            return {
                "success": len(results) > 0,
                "test_cases": results,
                "total_tests": len(test_cases),
                "rules_found": rules_found,
                "correct_step_sizes": correct_step_sizes,
                "mechanism_working": rules_found > 0 and correct_step_sizes > 0
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "mechanism_working": False
            }
    
    def _verify_default_values(self):
        """验证默认值问题"""
        print("🔍 验证默认值问题...")
        
        try:
            from core.trading_rules_preloader import get_trading_rules_preloader
            preloader = get_trading_rules_preloader()
            
            # 检查默认精度信息
            exchanges = ["bybit", "gate", "okx"]
            default_results = []
            
            for exchange in exchanges:
                try:
                    default_info = preloader._get_default_precision_info(exchange)
                    
                    default_results.append({
                        "exchange": exchange,
                        "default_info": default_info,
                        "step_size": default_info.get("step_size") if default_info else None,
                        "has_default": default_info is not None,
                        "success": True
                    })
                except Exception as e:
                    default_results.append({
                        "exchange": exchange,
                        "error": str(e),
                        "success": False
                    })
            
            # 检查是否所有交易所都有默认值
            all_have_defaults = all(r.get("has_default", False) for r in default_results)
            
            return {
                "success": len(default_results) > 0,
                "default_results": default_results,
                "all_exchanges_have_defaults": all_have_defaults,
                "bybit_default_step": next((r["step_size"] for r in default_results if r.get("exchange") == "bybit"), None)
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "all_exchanges_have_defaults": False
            }
    
    def _generate_final_verdict(self):
        """生成最终判决"""
        results = self.results["verification_results"]
        
        # ICNT问题是否解决
        icnt_resolved = results.get("icnt_futures_precision", {}).get("error_resolved", False)
        
        # SPK问题是否解决
        spk_resolved = results.get("spk_spot_precision", {}).get("error_resolved", False)
        
        # 步长机制是否工作
        mechanism_working = results.get("step_size_mechanism", {}).get("mechanism_working", False)
        
        # 默认值是否正确
        defaults_correct = results.get("default_values", {}).get("all_exchanges_have_defaults", False)
        
        # 总体判决
        overall_success = icnt_resolved and spk_resolved and mechanism_working
        
        self.results["final_verdict"] = {
            "icnt_error_resolved": icnt_resolved,
            "spk_error_resolved": spk_resolved,
            "step_size_mechanism_working": mechanism_working,
            "default_values_correct": defaults_correct,
            "overall_success": overall_success,
            "log_errors_100_percent_resolved": overall_success,
            "production_ready": overall_success,
            "verdict": "✅ 完全解决" if overall_success else "❌ 未完全解决"
        }
    
    def _save_results(self):
        """保存结果"""
        try:
            results_file = project_root / "critical_log_error_verification_results.json"
            with open(results_file, 'w', encoding='utf-8') as f:
                json.dump(self.results, f, indent=2, ensure_ascii=False)
            print(f"📊 结果已保存到: {results_file}")
        except Exception as e:
            print(f"⚠️ 保存结果失败: {e}")

def main():
    """主函数"""
    verifier = CriticalLogErrorVerifier()
    results = verifier.run_verification()
    
    # 打印结果
    verdict = results.get("final_verdict", {})
    print("\n" + "="*80)
    print("🚨 关键日志错误专项验证结果")
    print("="*80)
    print(f"ICNT期货错误解决: {'✅ 已解决' if verdict.get('icnt_error_resolved') else '❌ 未解决'}")
    print(f"SPK现货错误解决: {'✅ 已解决' if verdict.get('spk_error_resolved') else '❌ 未解决'}")
    print(f"步长机制工作: {'✅ 正常' if verdict.get('step_size_mechanism_working') else '❌ 异常'}")
    print(f"默认值正确: {'✅ 正确' if verdict.get('default_values_correct') else '❌ 错误'}")
    print("-"*80)
    print(f"总体判决: {verdict.get('verdict', '未知')}")
    print(f"日志错误100%解决: {'✅ 是' if verdict.get('log_errors_100_percent_resolved') else '❌ 否'}")
    print(f"生产环境就绪: {'✅ 就绪' if verdict.get('production_ready') else '❌ 未就绪'}")
    print("="*80)
    
    return results

if __name__ == "__main__":
    main()
