#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
交易对符号格式测试脚本
测试ICNT和SPK的符号转换是否正确
"""

import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_symbol_conversion():
    """测试符号转换"""
    print("🔍 测试交易对符号转换...")
    
    try:
        from exchanges.currency_adapter import get_exchange_symbol
        
        test_cases = [
            {"symbol": "ICNT", "exchange": "bybit", "market_type": "futures"},
            {"symbol": "ICNT-USDT", "exchange": "bybit", "market_type": "futures"},
            {"symbol": "SPK", "exchange": "bybit", "market_type": "spot"},
            {"symbol": "SPK-USDT", "exchange": "bybit", "market_type": "spot"},
        ]
        
        for case in test_cases:
            symbol = case["symbol"]
            exchange = case["exchange"]
            market_type = case["market_type"]
            
            converted = get_exchange_symbol(symbol, exchange, market_type)
            print(f"📊 {symbol} → {exchange} {market_type} → {converted}")
            
    except Exception as e:
        print(f"❌ 符号转换测试失败: {e}")

def test_bybit_api_call():
    """测试Bybit API调用"""
    print("\n🔍 测试Bybit API调用...")
    
    try:
        import asyncio
        from exchanges.bybit_exchange import BybitExchange
        
        # 创建临时实例
        api_key = os.getenv("BYBIT_API_KEY")
        api_secret = os.getenv("BYBIT_API_SECRET")
        
        if not api_key or not api_secret:
            print("❌ Bybit API密钥未配置")
            return
            
        exchange = BybitExchange(api_key, api_secret)
        
        async def test_api():
            # 测试不同的符号格式
            test_symbols = ["ICNTUSDT", "ICNUSDT", "SPKUSDT"]
            
            for symbol in test_symbols:
                print(f"📊 测试符号: {symbol}")
                try:
                    response = await exchange.get_instruments_info("linear", symbol)
                    if response and response.get("result", {}).get("list"):
                        instruments = response["result"]["list"]
                        if instruments:
                            instrument = instruments[0]
                            lot_size_filter = instrument.get("lotSizeFilter", {})
                            qty_step = lot_size_filter.get("qtyStep", "未知")
                            print(f"✅ {symbol} 找到，qtyStep={qty_step}")
                        else:
                            print(f"❌ {symbol} 未找到交易对数据")
                    else:
                        print(f"❌ {symbol} API返回空结果")
                except Exception as e:
                    if "symbol invalid" in str(e):
                        print(f"❌ {symbol} 交易对不存在")
                    else:
                        print(f"❌ {symbol} API调用失败: {e}")
        
        asyncio.run(test_api())
        
    except Exception as e:
        print(f"❌ Bybit API测试失败: {e}")

def test_real_bybit_symbols():
    """测试真实的Bybit交易对"""
    print("\n🔍 查找真实的Bybit交易对...")
    
    try:
        import asyncio
        from exchanges.bybit_exchange import BybitExchange
        
        api_key = os.getenv("BYBIT_API_KEY")
        api_secret = os.getenv("BYBIT_API_SECRET")
        
        if not api_key or not api_secret:
            print("❌ Bybit API密钥未配置")
            return
            
        exchange = BybitExchange(api_key, api_secret)
        
        async def find_symbols():
            try:
                # 获取所有线性合约
                response = await exchange.get_instruments_info("linear")
                if response and response.get("result", {}).get("list"):
                    instruments = response["result"]["list"]
                    
                    # 查找包含ICNT或SPK的交易对
                    found_symbols = []
                    for instrument in instruments:
                        symbol = instrument.get("symbol", "")
                        if "ICNT" in symbol or "SPK" in symbol:
                            lot_size_filter = instrument.get("lotSizeFilter", {})
                            qty_step = lot_size_filter.get("qtyStep", "未知")
                            found_symbols.append({
                                "symbol": symbol,
                                "qty_step": qty_step,
                                "status": instrument.get("status", "未知")
                            })
                    
                    if found_symbols:
                        print("✅ 找到相关交易对:")
                        for item in found_symbols:
                            print(f"   {item['symbol']}: qtyStep={item['qty_step']}, status={item['status']}")
                    else:
                        print("❌ 未找到ICNT或SPK相关的线性合约")
                        
                # 获取所有现货交易对
                response = await exchange.get_instruments_info("spot")
                if response and response.get("result", {}).get("list"):
                    instruments = response["result"]["list"]
                    
                    # 查找包含SPK的交易对
                    found_symbols = []
                    for instrument in instruments:
                        symbol = instrument.get("symbol", "")
                        if "SPK" in symbol:
                            lot_size_filter = instrument.get("lotSizeFilter", {})
                            base_precision = lot_size_filter.get("basePrecision", "未知")
                            found_symbols.append({
                                "symbol": symbol,
                                "base_precision": base_precision,
                                "status": instrument.get("status", "未知")
                            })
                    
                    if found_symbols:
                        print("✅ 找到相关现货交易对:")
                        for item in found_symbols:
                            print(f"   {item['symbol']}: basePrecision={item['base_precision']}, status={item['status']}")
                    else:
                        print("❌ 未找到SPK相关的现货交易对")
                        
            except Exception as e:
                print(f"❌ 查找交易对失败: {e}")
        
        asyncio.run(find_symbols())
        
    except Exception as e:
        print(f"❌ 真实交易对测试失败: {e}")

if __name__ == "__main__":
    print("🚀 开始交易对符号格式测试...")
    
    test_symbol_conversion()
    test_bybit_api_call()
    test_real_bybit_symbols()
    
    print("\n✅ 测试完成")
