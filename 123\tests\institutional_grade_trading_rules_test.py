#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
机构级别交易规则测试
三段进阶验证机制：基础核心测试、复杂系统级联测试、生产环境仿真测试
"""

import asyncio
import json
import time
import os
import sys
from typing import Dict, Any, List

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class InstitutionalGradeTradingRulesTest:
    def __init__(self):
        self.results = {
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "test_type": "机构级别交易规则测试",
            "phase_1_basic": {},
            "phase_2_system": {},
            "phase_3_production": {},
            "summary": {},
            "recommendations": []
        }
        
    async def phase_1_basic_core_tests(self):
        """① 基础核心测试：模块单元功能验证"""
        print("🔥 阶段1: 基础核心测试...")
        
        phase_results = {
            "preloader_initialization": await self._test_preloader_initialization(),
            "api_call_mechanism": await self._test_api_call_mechanism(),
            "default_precision_fallback": await self._test_default_precision_fallback(),
            "symbol_format_conversion": await self._test_symbol_format_conversion(),
            "amount_formatting": await self._test_amount_formatting()
        }
        
        self.results["phase_1_basic"] = phase_results
        
        success_count = sum(1 for result in phase_results.values() if result.get("success", False))
        total_count = len(phase_results)
        
        print(f"📊 阶段1结果: {success_count}/{total_count} 通过 ({success_count/total_count:.1%})")
        return success_count == total_count
    
    async def _test_preloader_initialization(self):
        """测试预加载器初始化"""
        try:
            from core.trading_rules_preloader import get_trading_rules_preloader
            preloader = get_trading_rules_preloader()
            
            return {
                "success": True,
                "preloader_type": type(preloader).__name__,
                "has_cache": hasattr(preloader, 'trading_rules'),
                "cache_size": len(preloader.trading_rules) if hasattr(preloader, 'trading_rules') else 0
            }
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def _test_api_call_mechanism(self):
        """测试API调用机制"""
        try:
            from core.trading_rules_preloader import get_trading_rules_preloader
            preloader = get_trading_rules_preloader()
            
            # 测试临时实例创建
            temp_instance = preloader._create_temporary_exchange_instance_sync("bybit")
            
            if temp_instance:
                # 测试同步API调用
                precision_info = preloader._get_precision_from_exchange_api_sync(
                    temp_instance, "ICNT", "futures"
                )
                
                return {
                    "success": True,
                    "temp_instance_created": True,
                    "api_call_success": precision_info is not None,
                    "step_size": precision_info.get("step_size") if precision_info else None,
                    "source": precision_info.get("source") if precision_info else None
                }
            else:
                return {
                    "success": False,
                    "temp_instance_created": False,
                    "error": "无法创建临时交易所实例"
                }
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def _test_default_precision_fallback(self):
        """测试默认精度回退机制"""
        try:
            from core.trading_rules_preloader import get_trading_rules_preloader
            preloader = get_trading_rules_preloader()
            
            # 测试各交易所的默认精度
            exchanges = ["bybit", "gate", "okx"]
            results = {}
            
            for exchange in exchanges:
                default_info = preloader._get_default_precision_info(exchange)
                results[exchange] = {
                    "has_default": default_info is not None,
                    "step_size": default_info.get("step_size") if default_info else None,
                    "source": default_info.get("source") if default_info else None
                }
            
            return {
                "success": True,
                "exchange_defaults": results,
                "all_exchanges_covered": len(results) == 3
            }
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def _test_symbol_format_conversion(self):
        """测试符号格式转换"""
        try:
            from exchanges.currency_adapter import get_exchange_symbol
            
            test_cases = [
                {"symbol": "ICNT", "exchange": "bybit", "market_type": "futures"},
                {"symbol": "SPK", "exchange": "bybit", "market_type": "spot"},
                {"symbol": "BTC", "exchange": "gate", "market_type": "spot"},
                {"symbol": "ETH", "exchange": "okx", "market_type": "futures"}
            ]
            
            results = {}
            for case in test_cases:
                key = f"{case['symbol']}_{case['exchange']}_{case['market_type']}"
                try:
                    converted = get_exchange_symbol(case["symbol"], case["exchange"], case["market_type"])
                    results[key] = {
                        "success": True,
                        "original": case["symbol"],
                        "converted": converted
                    }
                except Exception as e:
                    results[key] = {
                        "success": False,
                        "error": str(e)
                    }
            
            success_count = sum(1 for r in results.values() if r.get("success", False))
            
            return {
                "success": success_count == len(test_cases),
                "conversion_results": results,
                "success_rate": success_count / len(test_cases)
            }
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def _test_amount_formatting(self):
        """测试数量格式化"""
        try:
            from core.trading_rules_preloader import get_trading_rules_preloader
            preloader = get_trading_rules_preloader()
            
            # 测试关键的失败案例
            test_cases = [
                {"amount": 153.307, "exchange": "bybit", "symbol": "ICNT", "market_type": "futures"},
                {"amount": 321.995, "exchange": "bybit", "symbol": "SPK", "market_type": "spot"}
            ]
            
            results = {}
            for case in test_cases:
                key = f"{case['symbol']}_{case['market_type']}"
                try:
                    formatted = preloader.format_amount_unified(
                        case["amount"], case["exchange"], case["symbol"], case["market_type"]
                    )
                    results[key] = {
                        "success": formatted is not None,
                        "original": case["amount"],
                        "formatted": formatted
                    }
                except Exception as e:
                    results[key] = {
                        "success": False,
                        "error": str(e)
                    }
            
            success_count = sum(1 for r in results.values() if r.get("success", False))
            
            return {
                "success": success_count == len(test_cases),
                "formatting_results": results,
                "success_rate": success_count / len(test_cases)
            }
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def phase_2_system_integration_tests(self):
        """② 复杂系统级联测试：模块间交互逻辑验证"""
        print("🔥 阶段2: 复杂系统级联测试...")
        
        phase_results = {
            "multi_exchange_consistency": await self._test_multi_exchange_consistency(),
            "cache_mechanism_integrity": await self._test_cache_mechanism_integrity(),
            "error_handling_robustness": await self._test_error_handling_robustness(),
            "performance_benchmarks": await self._test_performance_benchmarks()
        }
        
        self.results["phase_2_system"] = phase_results
        
        success_count = sum(1 for result in phase_results.values() if result.get("success", False))
        total_count = len(phase_results)
        
        print(f"📊 阶段2结果: {success_count}/{total_count} 通过 ({success_count/total_count:.1%})")
        return success_count == total_count
    
    async def _test_multi_exchange_consistency(self):
        """测试多交易所一致性"""
        try:
            from core.trading_rules_preloader import get_trading_rules_preloader
            preloader = get_trading_rules_preloader()
            
            exchanges = ["bybit", "gate", "okx"]
            test_symbol = "BTC"
            
            results = {}
            for exchange in exchanges:
                for market_type in ["spot", "futures"]:
                    key = f"{exchange}_{market_type}"
                    try:
                        # 测试默认精度获取
                        default_info = preloader._get_default_precision_info(exchange)
                        
                        # 测试数量格式化
                        formatted = preloader.format_amount_unified(
                            100.123456, exchange, test_symbol, market_type
                        )
                        
                        results[key] = {
                            "success": True,
                            "has_default": default_info is not None,
                            "formatting_works": formatted is not None
                        }
                    except Exception as e:
                        results[key] = {
                            "success": False,
                            "error": str(e)
                        }
            
            success_count = sum(1 for r in results.values() if r.get("success", False))
            
            return {
                "success": success_count == len(results),
                "exchange_results": results,
                "consistency_rate": success_count / len(results)
            }
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def _test_cache_mechanism_integrity(self):
        """测试缓存机制完整性"""
        try:
            from core.trading_rules_preloader import get_trading_rules_preloader
            preloader = get_trading_rules_preloader()
            
            # 测试缓存统计
            initial_stats = preloader.get_cache_stats()
            
            # 执行一些操作来触发缓存
            test_operations = [
                ("bybit", "BTC", "spot"),
                ("gate", "ETH", "futures"),
                ("okx", "ADA", "spot")
            ]
            
            for exchange, symbol, market_type in test_operations:
                try:
                    preloader.format_amount_unified(100.0, exchange, symbol, market_type)
                except:
                    pass  # 忽略错误，只关心缓存统计
            
            final_stats = preloader.get_cache_stats()
            
            return {
                "success": True,
                "initial_stats": initial_stats,
                "final_stats": final_stats,
                "cache_activity": final_stats.get("cache_misses", 0) > initial_stats.get("cache_misses", 0)
            }
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def _test_error_handling_robustness(self):
        """测试错误处理健壮性"""
        try:
            from core.trading_rules_preloader import get_trading_rules_preloader
            preloader = get_trading_rules_preloader()
            
            # 测试各种错误场景
            error_scenarios = [
                {"exchange": "invalid_exchange", "symbol": "BTC", "market_type": "spot"},
                {"exchange": "bybit", "symbol": "", "market_type": "spot"},
                {"exchange": "bybit", "symbol": "BTC", "market_type": "invalid_market"},
                {"exchange": "", "symbol": "BTC", "market_type": "spot"}
            ]
            
            results = {}
            for i, scenario in enumerate(error_scenarios):
                key = f"error_scenario_{i+1}"
                try:
                    result = preloader.format_amount_unified(
                        100.0, scenario["exchange"], scenario["symbol"], scenario["market_type"]
                    )
                    results[key] = {
                        "handled_gracefully": True,
                        "result": result,
                        "scenario": scenario
                    }
                except Exception as e:
                    results[key] = {
                        "handled_gracefully": False,
                        "error": str(e),
                        "scenario": scenario
                    }
            
            graceful_count = sum(1 for r in results.values() if r.get("handled_gracefully", False))
            
            return {
                "success": graceful_count >= len(error_scenarios) * 0.8,  # 80%容错率
                "error_scenarios": results,
                "graceful_handling_rate": graceful_count / len(error_scenarios)
            }
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def _test_performance_benchmarks(self):
        """测试性能基准"""
        try:
            from core.trading_rules_preloader import get_trading_rules_preloader
            preloader = get_trading_rules_preloader()
            
            # 性能测试
            start_time = time.time()
            
            for _ in range(100):  # 100次调用
                try:
                    preloader.format_amount_unified(100.123, "bybit", "BTC", "spot")
                except:
                    pass
            
            end_time = time.time()
            total_time = (end_time - start_time) * 1000  # 转换为毫秒
            avg_time = total_time / 100
            
            # 性能要求：平均每次调用 < 10ms
            performance_acceptable = avg_time < 10.0
            
            return {
                "success": performance_acceptable,
                "total_time_ms": total_time,
                "average_time_ms": avg_time,
                "calls_per_second": 1000 / avg_time if avg_time > 0 else 0,
                "performance_threshold_met": performance_acceptable
            }
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def phase_3_production_simulation_tests(self):
        """③ 生产环境仿真测试：真实场景模拟"""
        print("🔥 阶段3: 生产环境仿真测试...")
        
        phase_results = {
            "real_trading_scenario": await self._test_real_trading_scenario(),
            "concurrent_load_test": await self._test_concurrent_load(),
            "edge_case_handling": await self._test_edge_cases(),
            "system_stability": await self._test_system_stability()
        }
        
        self.results["phase_3_production"] = phase_results
        
        success_count = sum(1 for result in phase_results.values() if result.get("success", False))
        total_count = len(phase_results)
        
        print(f"📊 阶段3结果: {success_count}/{total_count} 通过 ({success_count/total_count:.1%})")
        return success_count == total_count
    
    async def _test_real_trading_scenario(self):
        """测试真实交易场景"""
        try:
            from core.trading_rules_preloader import get_trading_rules_preloader
            preloader = get_trading_rules_preloader()
            
            # 模拟真实的交易场景
            real_scenarios = [
                {"amount": 153.307, "exchange": "bybit", "symbol": "ICNT", "market_type": "futures"},
                {"amount": 321.995, "exchange": "bybit", "symbol": "SPK", "market_type": "spot"},
                {"amount": 1000.0, "exchange": "gate", "symbol": "BTC", "market_type": "spot"},
                {"amount": 50.5, "exchange": "okx", "symbol": "ETH", "market_type": "futures"}
            ]
            
            results = {}
            for i, scenario in enumerate(real_scenarios):
                key = f"scenario_{i+1}"
                try:
                    formatted = preloader.format_amount_unified(
                        scenario["amount"], scenario["exchange"], 
                        scenario["symbol"], scenario["market_type"]
                    )
                    
                    results[key] = {
                        "success": formatted is not None,
                        "scenario": scenario,
                        "formatted_amount": formatted,
                        "precision_maintained": formatted != str(scenario["amount"])
                    }
                except Exception as e:
                    results[key] = {
                        "success": False,
                        "scenario": scenario,
                        "error": str(e)
                    }
            
            success_count = sum(1 for r in results.values() if r.get("success", False))
            
            return {
                "success": success_count == len(real_scenarios),
                "scenario_results": results,
                "success_rate": success_count / len(real_scenarios)
            }
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def _test_concurrent_load(self):
        """测试并发负载"""
        try:
            from core.trading_rules_preloader import get_trading_rules_preloader
            preloader = get_trading_rules_preloader()
            
            async def single_call():
                try:
                    return preloader.format_amount_unified(100.123, "bybit", "BTC", "spot")
                except:
                    return None
            
            # 并发测试
            start_time = time.time()
            tasks = [single_call() for _ in range(50)]  # 50个并发调用
            results = await asyncio.gather(*tasks, return_exceptions=True)
            end_time = time.time()
            
            successful_calls = sum(1 for r in results if r is not None and not isinstance(r, Exception))
            total_time = (end_time - start_time) * 1000
            
            return {
                "success": successful_calls >= 40,  # 至少80%成功
                "total_calls": len(tasks),
                "successful_calls": successful_calls,
                "success_rate": successful_calls / len(tasks),
                "total_time_ms": total_time,
                "concurrent_performance_acceptable": total_time < 5000  # 5秒内完成
            }
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def _test_edge_cases(self):
        """测试边缘情况"""
        try:
            from core.trading_rules_preloader import get_trading_rules_preloader
            preloader = get_trading_rules_preloader()
            
            edge_cases = [
                {"amount": 0.000001, "exchange": "bybit", "symbol": "BTC", "market_type": "spot"},
                {"amount": 999999999.0, "exchange": "gate", "symbol": "ETH", "market_type": "futures"},
                {"amount": float('inf'), "exchange": "okx", "symbol": "ADA", "market_type": "spot"},
                {"amount": -100.0, "exchange": "bybit", "symbol": "DOGE", "market_type": "futures"}
            ]
            
            results = {}
            for i, case in enumerate(edge_cases):
                key = f"edge_case_{i+1}"
                try:
                    result = preloader.format_amount_unified(
                        case["amount"], case["exchange"], case["symbol"], case["market_type"]
                    )
                    results[key] = {
                        "handled": True,
                        "case": case,
                        "result": result
                    }
                except Exception as e:
                    results[key] = {
                        "handled": True,  # 抛出异常也是一种处理方式
                        "case": case,
                        "error": str(e)
                    }
            
            return {
                "success": True,  # 只要不崩溃就算成功
                "edge_case_results": results,
                "all_cases_handled": len(results) == len(edge_cases)
            }
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def _test_system_stability(self):
        """测试系统稳定性"""
        try:
            from core.trading_rules_preloader import get_trading_rules_preloader
            preloader = get_trading_rules_preloader()
            
            # 长时间运行测试
            start_time = time.time()
            error_count = 0
            success_count = 0
            
            for i in range(200):  # 200次调用
                try:
                    result = preloader.format_amount_unified(
                        100.0 + i * 0.1, "bybit", "BTC", "spot"
                    )
                    if result:
                        success_count += 1
                except Exception:
                    error_count += 1
            
            end_time = time.time()
            total_time = (end_time - start_time) * 1000
            
            stability_rate = success_count / (success_count + error_count)
            
            return {
                "success": stability_rate >= 0.95,  # 95%稳定性要求
                "total_calls": 200,
                "successful_calls": success_count,
                "error_calls": error_count,
                "stability_rate": stability_rate,
                "total_time_ms": total_time,
                "average_time_ms": total_time / 200
            }
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def generate_summary(self):
        """生成测试摘要"""
        phase_1_success = all(result.get("success", False) for result in self.results["phase_1_basic"].values())
        phase_2_success = all(result.get("success", False) for result in self.results["phase_2_system"].values())
        phase_3_success = all(result.get("success", False) for result in self.results["phase_3_production"].values())
        
        total_tests = (
            len(self.results["phase_1_basic"]) + 
            len(self.results["phase_2_system"]) + 
            len(self.results["phase_3_production"])
        )
        
        successful_tests = (
            sum(1 for r in self.results["phase_1_basic"].values() if r.get("success", False)) +
            sum(1 for r in self.results["phase_2_system"].values() if r.get("success", False)) +
            sum(1 for r in self.results["phase_3_production"].values() if r.get("success", False))
        )
        
        overall_success = phase_1_success and phase_2_success and phase_3_success
        
        self.results["summary"] = {
            "phase_1_basic_passed": phase_1_success,
            "phase_2_system_passed": phase_2_success,
            "phase_3_production_passed": phase_3_success,
            "overall_success": overall_success,
            "total_tests": total_tests,
            "successful_tests": successful_tests,
            "success_rate": successful_tests / total_tests if total_tests > 0 else 0,
            "institutional_grade": overall_success and (successful_tests / total_tests) >= 0.95
        }
        
        # 生成建议
        if not overall_success:
            if not phase_1_success:
                self.results["recommendations"].append("基础核心测试失败，需要修复模块单元功能")
            if not phase_2_success:
                self.results["recommendations"].append("系统级联测试失败，需要修复模块间交互逻辑")
            if not phase_3_success:
                self.results["recommendations"].append("生产环境仿真测试失败，需要提高系统稳定性")
        else:
            self.results["recommendations"].append("所有测试通过，系统达到机构级别质量标准")
    
    async def run_full_test_suite(self):
        """运行完整测试套件"""
        print("🚀 开始机构级别交易规则测试...")
        print("=" * 60)
        
        # 三段进阶验证
        phase_1_passed = await self.phase_1_basic_core_tests()
        phase_2_passed = await self.phase_2_system_integration_tests()
        phase_3_passed = await self.phase_3_production_simulation_tests()
        
        self.generate_summary()
        
        # 保存结果
        output_file = "123/institutional_grade_test_results.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, ensure_ascii=False, indent=2)
        
        print("=" * 60)
        print(f"📊 测试完成，结果已保存到: {output_file}")
        print(f"📈 总体成功率: {self.results['summary']['success_rate']:.1%}")
        print(f"🏆 机构级别质量: {'✅ 达标' if self.results['summary']['institutional_grade'] else '❌ 未达标'}")
        
        return self.results

async def main():
    """主函数"""
    tester = InstitutionalGradeTradingRulesTest()
    results = await tester.run_full_test_suite()
    
    # 打印关键结果
    print("\n" + "="*50)
    print("🎯 三段进阶验证结果:")
    print(f"① 基础核心测试: {'✅ 通过' if results['summary']['phase_1_basic_passed'] else '❌ 失败'}")
    print(f"② 系统级联测试: {'✅ 通过' if results['summary']['phase_2_system_passed'] else '❌ 失败'}")
    print(f"③ 生产仿真测试: {'✅ 通过' if results['summary']['phase_3_production_passed'] else '❌ 失败'}")
    print(f"🏆 机构级别认证: {'✅ 通过' if results['summary']['institutional_grade'] else '❌ 未通过'}")
    
    return results

if __name__ == "__main__":
    asyncio.run(main())
